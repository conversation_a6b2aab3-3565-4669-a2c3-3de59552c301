import { getUserPermissions, simulateAction } from '../utils/permissionService.js'

/**
 * Get current user's inherited permissions
 * GET /api/me/permissions
 */
const getMyPermissions = async (req, res) => {
  try {
    const userId = req.user.id
    const permissions = await getUserPermissions(userId)

    // Group permissions by module
    const permissionsByModule = permissions.reduce((acc, permission) => {
      const moduleName = permission.module.name
      if (!acc[moduleName]) {
        acc[moduleName] = {
          module: permission.module,
          permissions: []
        }
      }
      acc[moduleName].permissions.push({
        id: permission.id,
        action: permission.action,
        description: permission.description,
        inheritedFrom: permission.inheritedFrom
      })
      return acc
    }, {})

    res.json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          email: req.user.email,
          username: req.user.username
        },
        totalPermissions: permissions.length,
        permissionsByModule,
        allPermissions: permissions
      }
    })
  } catch (error) {
    console.error('Get my permissions error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Simulate an action to test user's ability to perform it
 * POST /api/simulate-action
 * Body: { module: string, action: string }
 */
const simulateUserAction = async (req, res) => {
  try {
    const { module, action } = req.body
    const userId = req.user.id

    if (!module || !action) {
      return res.status(400).json({
        success: false,
        message: 'Module and action are required',
        example: {
          module: 'Users',
          action: 'create'
        }
      })
    }

    const simulation = await simulateAction(userId, module, action)

    res.json({
      success: true,
      data: {
        simulation,
        message: simulation.hasPermission
          ? `User can perform '${action}' on '${module}'`
          : `User cannot perform '${action}' on '${module}'`
      }
    })
  } catch (error) {
    console.error('Simulate action error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

export { getMyPermissions, simulateUserAction }
